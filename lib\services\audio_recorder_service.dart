/// 音频录制服务
///
/// 这个服务提供以下功能：
/// 1. 录制音频并实时显示波形
/// 2. 音频播放和波形可视化
/// 3. 支持iOS和Android平台
///
/// 使用方法：
/// ```dart
/// final audioService = AudioRecorderService();
/// await audioService.init();
///
/// // 开始录音
/// await audioService.startRecording();
///
/// // 停止录音
/// await audioService.stopRecording();
///
/// // 播放录音
/// await audioService.playRecording();
///
/// // 清理资源
/// audioService.dispose();
/// ```
///
/// 技术实现：
/// - 使用audio_waveforms包录制音频并提供实时波形显示
/// - 使用audio_waveforms包播放录制的音频
///
/// 配置参数：
/// - 采样率: 16000 Hz
/// - 最大录音时长: 15秒
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/app_logger.dart';
import '../utils/user_context.dart';

class AudioRecorderService {
  RecorderController _recorderController = RecorderController();
  PlayerController _playerController = PlayerController();
  StreamSubscription<Duration>? _recordingDurationSubscription;
  StreamSubscription<int>? _playingDurationSubscription;
  String _recordingPath = '';
  bool _isRecording = false;
  bool _hasRecorded = false;
  bool _isPlaying = false;
  String _audioTime = "00:00";
  List<double> _waveformData = [];

  // 状态变化回调
  VoidCallback? _onStateChanged;

  // 最大录音时长（毫秒）
  static const int maxRecordingDuration = 15000;

  // Getters
  bool get isRecording => _isRecording;
  bool get hasRecorded => _hasRecorded;
  bool get isPlaying => _isPlaying;
  String get audioTime => _audioTime;
  List<double> get waveformData => _waveformData;
  String get recordingPath => _recordingPath;
  RecorderController get recorderController => _recorderController;
  PlayerController get playerController => _playerController;

  // 设置状态变化回调
  void setStateChangeCallback(VoidCallback callback) {
    _onStateChanged = callback;
  }

  // 通知状态变化
  void _notifyStateChanged() {
    _onStateChanged?.call();
  }

  Future<void> init() async {
    // 设置波形更新频率
    // 录音器更新频率设置为100ms，平衡性能和流畅度
    _recorderController.updateFrequency = const Duration(milliseconds: 100);

    // 播放器更新频率设置为high模式，提供流畅的播放动画
    _playerController.updateFrequency = UpdateFrequency.high;

    // 初始化录音文件路径
    await _initializeRecordingPath();
    _notifyStateChanged();
    // 检查录音权限
    await _recorderController.checkPermission();
    // 确保目录存在
    final tempDir = await getTemporaryDirectory();
    if (!await Directory(tempDir.path).exists()) {
      await Directory(tempDir.path).create(recursive: true);
    }
    // 设置音频播放完成监听
    _playerController.onCompletion.listen((_) async {
      _isPlaying = false;

      // 清理播放时间监听
      _playingDurationSubscription?.cancel();
      _playingDurationSubscription = null;

      // 播放完成后保持显示总时长，而不是重置为00:00
      // 这样用户可以看到录音的总时长
      try {
        final totalDuration = await _playerController.getDuration(DurationType.max);
        final seconds = (totalDuration ~/ 1000).toString().padLeft(2, '0');
        final centiseconds = ((totalDuration % 1000) ~/ 10).toString().padLeft(2, '0');
        _audioTime = "$seconds:$centiseconds";
      } catch (e) {
        // 如果获取时长失败，保持当前时间显示
        AppLogger.warning('获取音频总时长失败: $e');
      }

      _notifyStateChanged();
    });

    // 设置播放完成模式为pause，这样播放完成后会暂停在结尾位置
    // 保持波形显示，用户可以重新播放
    _playerController.setFinishMode(finishMode: FinishMode.pause);

    // 监听录音状态变化
    _recorderController.onRecorderStateChanged.listen((state) {
      AppLogger.info('录音状态变化: $state');
      // 可以根据状态进行额外的处理
      _notifyStateChanged();
    });

    // 监听播放状态变化
    _playerController.onPlayerStateChanged.listen((state) {
      AppLogger.info('播放状态变化: $state');
      // 可以根据状态进行额外的处理
      _notifyStateChanged();
    });
  }

  Future<void> startRecording() async {
    if (_isRecording) return;

    try {
      // 删除之前的录音文件
      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      // 重置录音时间
      _audioTime = "00:00";
      _hasRecorded = false;

      // 使用audio_waveforms录制（用于波形显示）
      await _recorderController.record(
        path: _recordingPath,
        androidEncoder: AndroidEncoder.aac,
        androidOutputFormat: AndroidOutputFormat.mpeg4,
        iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
        sampleRate: 16000,
        bitRate: 256000,
      );

      _isRecording = true;
      _notifyStateChanged();

      // 使用audio_waveforms包提供的onCurrentDuration流来更新时间
      // 这个流每50ms更新一次，比自定义Timer的10ms更合适
      _recordingDurationSubscription = _recorderController.onCurrentDuration.listen((duration) {
        // 检查是否达到最大录音时长
        if (duration.inMilliseconds >= maxRecordingDuration) {
          stopRecording();
          return;
        }

        // 更新录音时间显示
        final totalMs = duration.inMilliseconds;
        final seconds = (totalMs ~/ 1000).toString().padLeft(2, '0');
        final centiseconds = ((totalMs % 1000) ~/ 10).toString().padLeft(2, '0');
        _audioTime = "$seconds:$centiseconds";
        _notifyStateChanged();
      });
    } catch (e, stackTrace) {
      AppLogger.warning('录音启动失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopRecording() async {
    if (!_isRecording) return;

    try {
      // 停止录音
      await _recorderController.stop();

      _isRecording = false;
      _hasRecorded = true;

      // 清理录音时间监听
      _recordingDurationSubscription?.cancel();
      _recordingDurationSubscription = null;
      _notifyStateChanged();
      await _preparePlayerController();
    } catch (e, stackTrace) {
      AppLogger.warning('停止录音失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> _preparePlayerController() async {
    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在: $_recordingPath');
        return;
      }

      // 为播放准备波形数据
      // shouldExtractWaveform: true 确保波形数据被提取并缓存
      // noOfSamples: 200 提供更详细的波形显示
      await _playerController.preparePlayer(
        path: _recordingPath,
        shouldExtractWaveform: true,
        noOfSamples: 200,
      );
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('准备播放器失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> playRecording() async {
    if (!_hasRecorded || _isPlaying) return;

    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在，无法播放');
        return;
      }
      // 使用PlayerController播放音频和显示波形
      await _playerController.startPlayer();
      _isPlaying = true;
      _notifyStateChanged();

      // 使用audio_waveforms包提供的onCurrentDurationChanged流来更新播放时间
      // 这比自定义Timer更准确且性能更好
      _playingDurationSubscription = _playerController.onCurrentDurationChanged.listen((durationMs) {
        if (!_isPlaying) return;

        // 更新播放时间显示（durationMs是毫秒数）
        final seconds = (durationMs ~/ 1000).toString().padLeft(2, '0');
        final centiseconds = ((durationMs % 1000) ~/ 10).toString().padLeft(2, '0');
        _audioTime = "$seconds:$centiseconds";
        _notifyStateChanged();
      });
    } catch (e, stackTrace) {
      AppLogger.warning('播放录音失败: $e', stackTrace: stackTrace);
      _isPlaying = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopPlayback() async {
    if (!_isPlaying) return;

    try {
      await _playerController.pausePlayer();
      _isPlaying = false;

      // 清理播放时间监听
      _playingDurationSubscription?.cancel();
      _playingDurationSubscription = null;

      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('停止播放失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> deleteRecording() async {
    try {
      await stopPlayback();

      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      _hasRecorded = false;
      _audioTime = "00:00";
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('删除录音失败: $e', stackTrace: stackTrace);
    }
  }

  /// 用户切换时重新初始化录音路径
  Future<void> reinitializeForUser() async {
    // 停止当前的录音和播放
    if (_isRecording) {
      await stopRecording();
    }
    if (_isPlaying) {
      await stopPlayback();
    }

    // 清理当前状态
    _hasRecorded = false;
    _audioTime = "00:00";

    // 重新初始化路径
    await _initializeRecordingPath();
    _notifyStateChanged();
  }

  /// 初始化录音文件路径
  Future<void> _initializeRecordingPath() async {
    final tempDir = await getTemporaryDirectory();

    // 使用用户上下文生成用户专属的录音文件路径
    final userContext = UserContext.instance;
    String fileName = 'recording.m4a';
    if (userContext.hasUserContext) {
      fileName = '${userContext.currentUserPhone}_recording.m4a';
    }
    _recordingPath = '${tempDir.path}/$fileName';

    // 检查是否有已存在的录音文件
    if (await File(_recordingPath).exists()) {
      _hasRecorded = true;
      await _preparePlayerController();
    }
  }

  void dispose() {
    _recordingDurationSubscription?.cancel();
    _playingDurationSubscription?.cancel();
    _recorderController.dispose();
    _playerController.dispose();
  }
}

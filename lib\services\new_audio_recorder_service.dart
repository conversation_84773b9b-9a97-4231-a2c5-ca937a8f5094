import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/app_logger.dart';
import '../utils/user_context.dart';

/// 新的音频录制服务，使用just_audio + record组合
/// 解决audio_waveforms包的波形显示问题
class NewAudioRecorderService {
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  
  String _recordingPath = '';
  bool _isRecording = false;
  bool _hasRecorded = false;
  bool _isPlaying = false;
  String _audioTime = "00:00";
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  
  // 波形数据
  List<double> _waveformData = [];
  StreamSubscription<Uint8List>? _amplitudeSubscription;
  
  // 状态变化回调
  VoidCallback? _onStateChanged;
  
  // 最大录音时长（毫秒）
  static const int maxRecordingDuration = 15000;

  // Getters
  bool get isRecording => _isRecording;
  bool get hasRecorded => _hasRecorded;
  bool get isPlaying => _isPlaying;
  String get audioTime => _audioTime;
  String get recordingPath => _recordingPath;
  List<double> get waveformData => _waveformData;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;

  void setStateChangedCallback(VoidCallback callback) {
    _onStateChanged = callback;
  }

  void _notifyStateChanged() {
    _onStateChanged?.call();
  }

  Future<void> init() async {
    // 初始化录音文件路径
    await _initializeRecordingPath();
    
    // 设置播放器监听
    _setupPlayerListeners();
    
    _notifyStateChanged();
  }

  /// 初始化录音文件路径
  Future<void> _initializeRecordingPath() async {
    final tempDir = await getTemporaryDirectory();
    
    // 使用用户上下文生成用户专属的录音文件路径
    final userContext = UserContext.instance;
    String fileName = 'recording.m4a';
    if (userContext.hasUserContext) {
      fileName = '${userContext.currentUserPhone}_recording.m4a';
    }
    _recordingPath = '${tempDir.path}/$fileName';
    
    // 检查是否有已存在的录音文件
    if (await File(_recordingPath).exists()) {
      _hasRecorded = true;
      await _loadAudioFile();
    }
  }

  /// 设置播放器监听
  void _setupPlayerListeners() {
    // 监听播放位置变化
    _player.positionStream.listen((position) {
      _currentPosition = position;
      if (_isPlaying) {
        _updateTimeDisplay(position);
      }
    });

    // 监听播放完成
    _player.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        _isPlaying = false;
        _currentPosition = _totalDuration;
        _updateTimeDisplay(_totalDuration);
        _notifyStateChanged();
      }
    });

    // 监听总时长变化
    _player.durationStream.listen((duration) {
      if (duration != null) {
        _totalDuration = duration;
        _notifyStateChanged();
      }
    });
  }

  /// 更新时间显示
  void _updateTimeDisplay(Duration position) {
    final totalMs = position.inMilliseconds;
    final seconds = (totalMs ~/ 1000).toString().padLeft(2, '0');
    final centiseconds = ((totalMs % 1000) ~/ 10).toString().padLeft(2, '0');
    _audioTime = "$seconds:$centiseconds";
    _notifyStateChanged();
  }

  /// 开始录音
  Future<void> startRecording() async {
    if (_isRecording) return;

    try {
      // 检查权限
      if (!await _recorder.hasPermission()) {
        AppLogger.warning('没有录音权限');
        return;
      }

      // 删除之前的录音文件
      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      // 重置状态
      _audioTime = "00:00";
      _hasRecorded = false;
      _waveformData.clear();
      _currentPosition = Duration.zero;
      _totalDuration = Duration.zero;

      // 开始录音
      await _recorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _recordingPath,
      );

      _isRecording = true;
      _notifyStateChanged();

      // 开始监听振幅变化来生成波形数据
      _startAmplitudeMonitoring();

    } catch (e, stackTrace) {
      AppLogger.warning('录音启动失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  /// 监听振幅变化生成波形数据
  void _startAmplitudeMonitoring() {
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      // 检查录音时长
      final recordingDuration = _waveformData.length * 100;
      if (recordingDuration >= maxRecordingDuration) {
        stopRecording();
        return;
      }

      // 生成模拟波形数据（实际应用中可以从麦克风获取真实振幅）
      final amplitude = (0.1 + (0.9 * (0.5 + 0.5 * 
          (DateTime.now().millisecondsSinceEpoch % 1000) / 1000)));
      _waveformData.add(amplitude);

      // 更新时间显示
      final seconds = (recordingDuration ~/ 1000).toString().padLeft(2, '0');
      final centiseconds = ((recordingDuration % 1000) ~/ 10).toString().padLeft(2, '0');
      _audioTime = "$seconds:$centiseconds";
      
      _notifyStateChanged();
    });
  }

  /// 停止录音
  Future<void> stopRecording() async {
    if (!_isRecording) return;

    try {
      final path = await _recorder.stop();
      
      _isRecording = false;
      _hasRecorded = true;
      
      if (path != null) {
        _recordingPath = path;
        await _loadAudioFile();
      }
      
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('停止录音失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  /// 加载音频文件
  Future<void> _loadAudioFile() async {
    try {
      await _player.setFilePath(_recordingPath);
      AppLogger.info('音频文件加载完成: $_recordingPath');
    } catch (e, stackTrace) {
      AppLogger.warning('加载音频文件失败: $e', stackTrace: stackTrace);
    }
  }

  /// 播放录音
  Future<void> playRecording() async {
    if (!_hasRecorded || _isPlaying) return;

    try {
      await _player.play();
      _isPlaying = true;
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('播放录音失败: $e', stackTrace: stackTrace);
      _isPlaying = false;
      _notifyStateChanged();
    }
  }

  /// 停止播放
  Future<void> stopPlayback() async {
    if (!_isPlaying) return;

    try {
      await _player.pause();
      _isPlaying = false;
      _updateTimeDisplay(_currentPosition);
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('停止播放失败: $e', stackTrace: stackTrace);
    }
  }

  /// 重置播放位置
  Future<void> resetPlaybackPosition() async {
    try {
      if (_isPlaying) {
        await stopPlayback();
      }
      
      await _player.seek(Duration.zero);
      _currentPosition = Duration.zero;
      _audioTime = "00:00";
      _notifyStateChanged();
      
      AppLogger.info('播放位置已重置到开始');
    } catch (e, stackTrace) {
      AppLogger.warning('重置播放位置失败: $e', stackTrace: stackTrace);
    }
  }

  /// 删除录音
  Future<void> deleteRecording() async {
    try {
      await stopPlayback();

      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      _hasRecorded = false;
      _audioTime = "00:00";
      _waveformData.clear();
      _currentPosition = Duration.zero;
      _totalDuration = Duration.zero;
      _notifyStateChanged();
    } catch (e, stackTrace) {
      AppLogger.warning('删除录音失败: $e', stackTrace: stackTrace);
    }
  }

  /// 用户切换时重新初始化
  Future<void> reinitializeForUser() async {
    // 停止当前的录音和播放
    if (_isRecording) {
      await stopRecording();
    }
    if (_isPlaying) {
      await stopPlayback();
    }

    // 清理当前状态
    _hasRecorded = false;
    _audioTime = "00:00";
    _waveformData.clear();
    _currentPosition = Duration.zero;
    _totalDuration = Duration.zero;

    // 重新初始化路径
    await _initializeRecordingPath();
    _notifyStateChanged();
  }

  void dispose() {
    _amplitudeSubscription?.cancel();
    _recorder.dispose();
    _player.dispose();
  }
}
